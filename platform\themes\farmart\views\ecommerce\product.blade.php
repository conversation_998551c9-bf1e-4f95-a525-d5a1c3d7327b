@php
    Theme::layout('full-width');
    Theme::set('bodyClass', 'single-product');
@endphp

<!-- Hide product gallery thumbnails -->
<style>
    @media (max-width: 991px) {
        .bb-product-gallery-thumbnails {
            display: none !important;
        }

        .bb-product-gallery-images {
            margin-bottom: 0 !important;
        }
    }
</style>

<!-- Direct styles for price tag -->
<style>
    /* DESKTOP PRODUCT DETAIL PAGE LARGE PRICE */
    @media (min-width: 993px) {
        /* Main price styling */
        .product-price-wrapper .single-price-display .current-price .amount,
        .product-price-wrapper .single-price-display .product-price-original .amount,
        .product-price-wrapper .single-price-display ins.current-price .amount {
            font-size: 42px !important;
            font-weight: 700 !important;
            color: #ff6633 !important;
            line-height: 1.2 !important;
            text-decoration: none !important;
        }

        /* Original price styling - only one should exist */
        .product-price-wrapper .single-price-display .original-price .amount,
        .product-price-wrapper .single-price-display del.original-price .amount {
            font-size: 22px !important;
            color: #999 !important;
            text-decoration: line-through !important;
            margin-left: 10px !important;
        }

        /* Ensure proper order: current price first, then original price */
        .product-price-wrapper .single-price-display .product-price-sale {
            display: flex !important;
            align-items: center !important;
            flex-direction: row !important;
        }

        .product-price-wrapper .single-price-display .current-price {
            order: 1 !important;
        }

        .product-price-wrapper .single-price-display .original-price {
            order: 2 !important;
        }

        /* Add grey divider below price tag on desktop */
        .product-price-wrapper::after {
            content: "";
            display: block;
            width: 100%;
            height: 1px;
            background-color: #e0e0e0;
            margin-top: 15px;
        }
    }

    @media (max-width: 991px) {
        /* Force price tag styles */
        .product-inner .product-details .mobile-price-tag,
        .product-inner .product-details [style*="background-color: #ff6633"] {
            display: block !important;
            background-color: #ff6633 !important;
            margin: -10px -15px 15px -15px !important;
            padding: 10px 15px !important;
            width: calc(100% + 30px) !important;
            position: relative !important;
            color: #fff !important;
        }

        /* Move price tag to be right after the image with no gap */
        .product-details-content .product-price-wrapper {
            order: -999 !important;
            background-color: #ff6633 !important;
            margin: -15px 0 15px 0 !important;
            padding: 10px 15px !important;
            width: 100% !important;
            position: relative !important;
            color: #fff !important;
            display: block !important;
            box-sizing: border-box !important;
        }

        .product-details-content .product-price-wrapper .product-price {
            background-color: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            color: #fff !important;
        }

        /* Ensure price tag width matches image width */
        @media (max-width: 991px) {
            .product-details-content .product-details {
                width: auto !important;
                max-width: 100% !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
            }

            .product-details-content {
                padding-left: 0 !important;
                padding-right: 0 !important;
            }

            .bb-product-gallery-wrapper,
            .bb-product-gallery,
            .bb-product-gallery-images,
            .bb-product-gallery-images img,
            .product-details-content .product-details .product-price {
                width: 100% !important;
                max-width: 100% !important;
                box-sizing: border-box !important;
            }

            /* Force the price tag to be the same width as the image */
            .col-lg-5.col-md-12,
            .col-lg-4.col-md-8.ps-4.product-details-content {
                padding-left: 0 !important;
                padding-right: 0 !important;
            }
            /* Ensure main title is always visible */
            .entry-product-header .product_title.entry-title {
                display: block !important;
                visibility: visible !important;
            }

            /* Nuclear approach - hide ALL images with product name that are NOT in gallery */
            img[alt*="Jellyfish Colorful Lamp"]:not(.custom-product-gallery *):not(.product-gallery *):not(.gallery *) {
                display: none !important;
            }

            img[src*="Jellyfish-Colorful-Lamp"]:not(.custom-product-gallery *):not(.product-gallery *):not(.gallery *) {
                display: none !important;
            }

            /* Target specific containers that might have duplicate images */
            .zoom-result img:not([src*="data:"]) {
                display: none !important;
            }
        }

        /* Make product details content a flex container with column direction */
        .product-details-content .product-details {
            display: flex !important;
            flex-direction: column !important;
        }

        /* Style price tag text in product page */
        .product-details-content .product-price-wrapper .product-price .product-price-sale,
        .product-details-content .product-price-wrapper .product-price .product-price-original,
        .product-details-content .product-price-wrapper .single-price-display .current-price,
        .product-details-content .product-price-wrapper .single-price-display .original-price {
            color: #fff !important;
        }

        .product-details-content .product-price-wrapper .price-amount bdi .amount,
        .product-details-content .product-price-wrapper .current-price .amount,
        .product-details-content .product-price-wrapper ins .amount {
            color: #fff !important;
            font-size: 18px !important;
            font-weight: 700 !important;
            text-decoration: none !important;
        }

        .product-details-content .product-price-wrapper del .price-amount bdi .amount,
        .product-details-content .product-price-wrapper .original-price .amount {
            color: #fff !important;
            font-size: 14px !important;
            text-decoration: line-through !important;
            opacity: 0.8 !important;
        }

        /* Desktop Price Tag - Make main price bigger on desktop */
        @media (min-width: 993px) {
            /* Main product detail page price */
            .product-details-content .product-details .product-price .price-amount bdi .amount,
            .bb-product-price-text,
            .bb-product-price .price-amount bdi .amount,
            .product-price-sale ins .price-amount bdi .amount,
            .product-price-original .price-amount bdi .amount {
                font-size: 28px !important;
                font-weight: 700 !important;
            }

            /* Keep original price smaller */
            .product-details-content .product-details .product-price del .price-amount bdi .amount,
            .bb-product-price-text-old,
            .product-price-sale del .price-amount bdi .amount {
                font-size: 18px !important;
                text-decoration: line-through !important;
                opacity: 0.8 !important;
            }
        }

        /* Force content styles */
        .product-inner .product-details [style*="display: flex"] {
            display: flex !important;
        }

        /* For sale prices, use column layout */
        .product-inner .product-details [style*="flex-direction: column"] {
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: flex-start !important;
            min-height: 30px !important;
        }

        /* For regular prices, use row layout */
        .product-inner .product-details [style*="align-items: center"] {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            height: 30px !important;
        }

        /* Force price styles */
        .product-inner .product-details [style*="font-size: 18px"] {
            font-size: 18px !important;
            font-weight: 700 !important;
            color: #fff !important;
        }

        .product-inner .product-details [style*="text-decoration: line-through"] {
            font-size: 14px !important;
            text-decoration: line-through !important;
            opacity: 0.8 !important;
            color: #fff !important;
        }

        /* Hide desktop price on mobile */
        .product-inner .product-details .d-none.d-md-block {
            display: none !important;
        }

        /* Force content box padding */
        .product-inner .product-details .product-content-box {
            padding-top: 10px !important;
        }
    }
</style>

<!-- Direct script to ensure price tag is visible and positioned correctly -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        if (window.innerWidth <= 991) {
            // Force all price tags to be visible
            var priceTags = document.querySelectorAll('.product-inner .product-details [style*="background-color: #ff6633"]');
            priceTags.forEach(function(tag) {
                tag.style.display = 'block';
                tag.style.backgroundColor = '#ff6633';
                tag.style.color = '#fff';
                tag.style.margin = '-10px -15px 15px -15px';
                tag.style.padding = '10px 15px';
                tag.style.width = 'calc(100% + 30px)';
                tag.style.position = 'relative';
            });

            // Hide desktop price display
            var desktopPrices = document.querySelectorAll('.product-inner .product-details .d-none.d-md-block');
            desktopPrices.forEach(function(price) {
                price.style.display = 'none';
            });

            // Move price tag to be right after the image
            var productDetailsContent = document.querySelector('.product-details-content');
            var productGallery = document.querySelector('.bb-product-gallery-wrapper');

            if (productDetailsContent && productGallery) {
                // Make the container a flex container with column direction
                productDetailsContent.style.display = 'flex';
                productDetailsContent.style.flexDirection = 'column';

                // Get the price wrapper element
                var priceWrapper = productDetailsContent.querySelector('.product-price-wrapper');

                if (priceWrapper) {
                    // Apply styles to the price wrapper
                    priceWrapper.style.order = '-999';
                    priceWrapper.style.backgroundColor = '#ff6633';
                    priceWrapper.style.margin = '-15px 0 15px 0';
                    priceWrapper.style.padding = '10px 15px';
                    priceWrapper.style.position = 'relative';
                    priceWrapper.style.color = '#fff';
                    priceWrapper.style.display = 'block';
                    priceWrapper.style.boxSizing = 'border-box';

                    // Get the main product image
                    var mainImage = document.querySelector('.bb-product-gallery-images img');
                    if (mainImage) {
                        // Force the price tag to match the exact width of the main image
                        setTimeout(function() {
                            var imageWidth = mainImage.offsetWidth;
                            priceWrapper.style.width = imageWidth + 'px';
                            priceWrapper.style.maxWidth = '100%';
                        }, 100);
                    }

                    // Style the price text
                    var priceAmounts = priceWrapper.querySelectorAll('.price-amount bdi .amount');
                    priceAmounts.forEach(function(amount) {
                        amount.style.color = '#fff';
                        // Use larger font size for desktop (993px+)
                        amount.style.fontSize = window.innerWidth >= 993 ? '28px' : '18px';
                        amount.style.fontWeight = '700';
                    });

                    // Style the original price if it exists
                    var originalPrice = priceWrapper.querySelector('del .price-amount bdi .amount');
                    if (originalPrice) {
                        // Use larger font size for desktop original price too
                        originalPrice.style.fontSize = window.innerWidth >= 993 ? '18px' : '14px';
                        originalPrice.style.textDecoration = 'line-through';
                        originalPrice.style.opacity = '0.8';
                    }
                }
            }
        }
    });
</script>

<!-- Hide breadcrumbs on mobile and tablet -->
<style>
    @media (max-width: 991px) {
        .page-breadcrumbs,
        nav[aria-label="breadcrumb"],
        .breadcrumb,
        .page-header .page-breadcrumbs {
            display: none !important;
        }
    }
</style>

<!-- Wishlist button styling -->
<style>
    /* Loading indicator for wishlist button */
    .wishlist-button .wishlist.loading:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 50%;
        z-index: 1;
    }

    .wishlist-button .wishlist.loading:after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin-top: -10px;
        margin-left: -10px;
        border: 2px solid #ff6633;
        border-top-color: transparent;
        border-radius: 50%;
        z-index: 2;
        animation: spin 0.8s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .wishlist-button .wishlist {
        background-color: rgba(0, 0, 0, 0.15) !important;
        color: #fff !important;
        transition: all 0.3s ease;
    }

    .wishlist-button .wishlist.added-to-wishlist {
        background-color: #ff6633 !important;
        color: #fff !important;
    }

    .wishlist-button .wishlist.added-to-wishlist use {
        fill: #fff !important;
    }
</style>

<!-- Inline styles for thinner sticky bar and fixed positioning -->
<style>
    @media (max-width: 991px) {
        /* Base styles for the sticky bar */
        #sticky-add-to-cart .sticky-atc-wrap {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            position: fixed !important;
            bottom: 60px !important; /* Increased space for the footer mobile bar */
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            z-index: 998 !important; /* Lower than maximum but still high */
            transform: none !important;
            background-color: #fff !important;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
            padding: 3px 0 !important; /* Further reduced padding */
            border-bottom: 1px solid #eee !important; /* Add a border to separate from footer */
        }

        /* Ensure the footer mobile bar is visible */
        .footer-mobile {
            display: block !important;
            position: fixed !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 997 !important; /* Lower than the add-to-cart bar */
            background-color: #fff !important;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
            padding: 0 !important; /* Remove padding */
            height: 60px !important; /* Fixed height */
        }

        /* Add more bottom padding to the page */
        .single-product .product-detail-container {
            padding-bottom: 170px !important; /* Increased padding */
        }

        /* Improve the layout of the sticky add-to-cart bar */
        #sticky-add-to-cart .sticky-atc-btn {
            display: flex !important;
            flex-wrap: nowrap !important;
            align-items: center !important;
            padding: 0 10px !important;
            width: 100% !important;
        }

        #sticky-add-to-cart .sticky-bar-content {
            display: flex !important;
            align-items: center !important;
            width: 100% !important;
            padding: 0 !important;
        }

        #sticky-add-to-cart .quantity {
            flex: 0 0 auto !important;
            margin-bottom: 0 !important;
            margin-right: 10px !important;
            display: flex !important;
            align-items: center !important;
        }

        #sticky-add-to-cart .quantity .label-quantity {
            margin-right: 5px !important;
            font-size: 13px !important;
            margin-bottom: 0 !important;
        }

        #sticky-add-to-cart .quantity .qty-box {
            height: 28px !important;
        }

        #sticky-add-to-cart .quantity .svg-icon {
            width: 24px !important;
            height: 24px !important;
        }

        #sticky-add-to-cart .quantity .svg-icon svg {
            width: 12px !important;
            height: 12px !important;
        }

        #sticky-add-to-cart .quantity .qty {
            height: 24px !important;
            font-size: 14px !important;
            width: 40px !important;
        }

        #sticky-add-to-cart .btn {
            flex: 1 !important;
            margin: 0 5px !important;
            padding: 6px 5px !important;
            font-size: 13px !important;
            white-space: nowrap !important;
            margin-bottom: 0 !important;
        }

        /* Style the footer mobile bar */
        .footer-mobile .menu--footer {
            margin: 0 !important;
            padding: 0 !important;
            display: flex !important;
            justify-content: space-around !important;
            align-items: center !important;
            height: 100% !important;
        }

        .footer-mobile .menu--footer li {
            flex: 1 !important;
            text-align: center !important;
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            padding: 5px 0 !important;
        }

        .footer-mobile .menu--footer li a {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            justify-content: center !important;
            height: 100% !important;
        }

        .footer-mobile .menu--footer li i {
            font-size: 18px !important;
            margin-bottom: 2px !important;
        }

        .footer-mobile .menu--footer li span {
            font-size: 10px !important;
            line-height: 1 !important;
        }

        /* Fix for the row and container */
        #sticky-add-to-cart .container,
        #sticky-add-to-cart .row {
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Fix for the d-flex elements */
        .d-flex {
            display: flex !important;
        }

        .align-items-center {
            align-items: center !important;
        }

        .flex-grow-1 {
            flex-grow: 1 !important;
        }

        .me-1, .me-2 {
            margin-right: 0.25rem !important;
        }

        .mb-0 {
            margin-bottom: 0 !important;
        }
    }
</style>
{!! Theme::partial('page-header', ['size' => 'xxxl']) !!}

<div class="product-detail-container">
    <div class="bg-light py-md-5 px-lg-3 px-2">
        <div class="container-xxxl rounded-7 bg-white py-lg-5 py-md-4 py-3 px-3 px-md-4 px-lg-5">
            <div class="row">
                <div class="col-lg-5 col-md-12 mb-md-5 pb-md-5 mb-3 position-relative">
                    @include('theme.farmart::partials.ecommerce.custom-product-gallery')

                    @if (EcommerceHelper::isWishlistEnabled())
                        @php
                            $productWishlistIds = \Theme\Farmart\Supports\Wishlist::getWishlistIds([$product->id]);
                        @endphp
                        <div class="wishlist-button product-wishlist-button" style="position: absolute; top: 15px; right: 15px; z-index: 10;">
                            <a
                                class="wishlist btn btn-sm rounded-circle @if (!empty($productWishlistIds) && in_array($product->id, $productWishlistIds)) added-to-wishlist @endif"
                                data-url="{{ route('public.wishlist.add', $product->id) }}"
                                href="#"
                                title="{{ __('Add to Wishlist') }}"
                                style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; border: none; background-color: rgba(0, 0, 0, 0.15); color: #fff; box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); position: relative;"
                            >
                                <span class="svg-icon" style="width: 20px; height: 20px;">
                                    <svg>
                                        <use
                                            href="#svg-icon-wishlist"
                                            xlink:href="#svg-icon-wishlist"
                                        ></use>
                                    </svg>
                                </span>
                            </a>
                        </div>
                    @endif
                </div>
                <div class="col-lg-4 col-md-8 ps-4 product-details-content">
                    <div class="product-details js-product-content">
                        <div class="entry-product-header">
                            <div class="product-header-left">
                                <h1 class="fs-5 fw-normal product_title entry-title">{{ $product->name }}</h1>
                                <div class="product-entry-meta">
                                    @if ($product->brand_id)
                                        <p class="mb-0 me-2 pe-2 text-secondary">{{ __('Brand') }}: <a
                                                href="{{ $product->brand->url }}"
                                            >{{ $product->brand->name }}</a></p>
                                    @endif

                                    @if (EcommerceHelper::isReviewEnabled())
                                        <a
                                            class="anchor-link"
                                            href="#product-reviews-tab"
                                        >
                                            {!! Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]) !!}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="product-price-wrapper">
                            {!! Theme::partial('ecommerce.product-price', compact('product')) !!}
                        </div>

                        @if (is_plugin_active('marketplace') && $product->store_id)
                            <div class="product-meta-sold-by my-2">
                                <span class="d-inline-block me-1">{{ __('Sold By') }}: </span>
                                <a href="{{ $product->store->url }}">
                                    {{ $product->store->name }}
                                </a>
                            </div>
                        @endif

                        <div class="ps-list--dot">
                            {!! apply_filters('ecommerce_before_product_description', null, $product) !!}
                            {!! BaseHelper::clean($product->description) !!}
                            {!! apply_filters('ecommerce_after_product_description', null, $product) !!}
                        </div>

                        {!! Theme::partial('ecommerce.product-availability', compact('product', 'productVariation')) !!}
                        @if (Botble\Ecommerce\Facades\FlashSale::isEnabled() && ($flashSale = $product->latestFlashSales()->first()))
                            <div class="deal-expire-date p-4 bg-light mb-2">
                                <div class="row">
                                    <div class="col-xxl-5 d-md-flex justify-content-center align-items-center">
                                        <div class="deal-expire-text mb-2">
                                            <div class="fw-bold text-uppercase">{{ __('Hurry up! Sale end in') }}</div>
                                        </div>
                                    </div>
                                    <div class="col-xxl-7">
                                        <div class="countdown-wrapper d-none">
                                            <div
                                                class="expire-countdown col-auto"
                                                data-expire="{{ Carbon\Carbon::now()->diffInSeconds($flashSale->end_date) }}"
                                            >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row align-items-center my-3">
                                    <div class="deal-sold row mt-2">
                                        @if (Botble\Ecommerce\Facades\FlashSale::isShowSaleCountLeft())
                                            <div class="deal-text col-auto">
                                                <span class="sold fw-bold">
                                                    <span class="text">{{ __('Sold') }}: </span>
                                                    <span class="value">{{ $flashSale->sale_count_left_label }}</span>
                                                </span>
                                            </div>
                                        @endif
                                        <div class="deal-progress col">
                                            <div class="progress">
                                                <div
                                                    class="progress-bar"
                                                    role="progressbar"
                                                    aria-valuenow="{{ $flashSale->sale_count_left_percent }}"
                                                    aria-valuemin="0"
                                                    aria-valuemax="100"
                                                    style="width: {{ $flashSale->sale_count_left_percent }}%;"
                                                >
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        {!! Theme::partial(
                            'ecommerce.product-cart-form',
                            compact('product', 'selectedAttrs', 'productVariation') + [
                                'withButtons' => true,
                                'withVariations' => true,
                                'withProductOptions' => true,
                                'wishlistIds' => \Theme\Farmart\Supports\Wishlist::getWishlistIds([$product->id]),
                                'withBuyNow' => true,
                            ],
                        ) !!}
                        <div class="meta-sku @if (!$product->sku) d-none @endif">
                            <span class="meta-label d-inline-block me-1">{{ __('SKU') }}:</span>
                            <span class="meta-value" data-bb-value="product-sku">{{ $product->sku }}</span>
                        </div>
                        @if ($product->categories->isNotEmpty())
                            <div class="meta-categories">
                                <span class="meta-label d-inline-block me-1">{{ __('Categories') }}: </span>
                                @foreach ($product->categories as $category)
                                    <a href="{{ $category->url }}">{{ $category->name }}</a>@if (!$loop->last),@endif
                                @endforeach
                            </div>
                        @endif
                        @if ($product->tags->isNotEmpty())
                            <div class="meta-categories">
                                <span class="meta-label d-inline-block me-1">{{ __('Tags') }}: </span>
                                @foreach ($product->tags as $tag)
                                    <a href="{{ $tag->url }}">{{ $tag->name }}</a>@if (!$loop->last),@endif
                                @endforeach
                            </div>
                        @endif
                        @if (theme_option('social_share_enabled', 'yes') == 'yes')
                            <div class="my-5">
                                {!! Theme::partial('share-socials', compact('product')) !!}
                            </div>
                        @endif
                    </div>
                </div>
                <div class="col-lg-3 col-md-4">
                    {!! Theme::partial('ecommerce.aliexpress-style-cart', compact('product')) !!}
                </div>
            </div>
        </div>
    </div>
    <div class="container-xxxl">
        <div class="row product-detail-tabs mt-3 mb-4">
            <div class="col-md-3">
                <div
                    class="nav flex-column nav-pills me-3"
                    id="product-detail-tabs"
                    role="tablist"
                    aria-orientation="vertical"
                >
                    <a
                        class="nav-link active"
                        id="product-description-tab"
                        data-bs-toggle="pill"
                        type="button"
                        href="#product-description"
                        role="tab"
                        aria-controls="product-description"
                        aria-selected="true"
                    >
                        {{ __('Description') }}
                    </a>
                    @if (EcommerceHelper::isReviewEnabled())
                        <a
                            class="nav-link"
                            id="product-reviews-tab"
                            data-bs-toggle="pill"
                            type="button"
                            href="#product-reviews"
                            role="tab"
                            aria-controls="product-reviews"
                            aria-selected="false"
                        >
                            {{ __('Reviews') }} ({{ $product->reviews_count }})
                        </a>
                    @endif
                    @if (is_plugin_active('marketplace') && $product->store_id)
                        <a
                            class="nav-link"
                            id="product-vendor-info-tab"
                            data-bs-toggle="pill"
                            type="button"
                            href="#product-vendor-info"
                            role="tab"
                            aria-controls="product-vendor-info"
                            aria-selected="false"
                        >
                            {{ __('Vendor Info') }}
                        </a>
                    @endif
                    @if (is_plugin_active('faq') && count($product->faq_items) > 0)
                        <a
                            class="nav-link"
                            id="product-faqs-tab"
                            data-bs-toggle="pill"
                            type="button"
                            href="#product-faqs"
                            role="tab"
                            aria-controls="product-faqs"
                            aria-selected="false"
                        >
                            {{ __('Questions & Answers') }}
                        </a>
                    @endif
                </div>
            </div>
            <div class="col-md-9">
                <div
                    class="tab-content"
                    id="product-detail-tabs-content"
                >
                    <div
                        class="tab-pane fade show active"
                        id="product-description"
                        role="tabpanel"
                        aria-labelledby="product-description-tab"
                    >
                        <div class="ck-content">
                            {!! BaseHelper::clean($product->content) !!}
                        </div>

                        {!! apply_filters(BASE_FILTER_PUBLIC_COMMENT_AREA, null, $product) !!}
                    </div>
                    @if (EcommerceHelper::isReviewEnabled())
                        <div
                            class="tab-pane fade"
                            id="product-reviews"
                            role="tabpanel"
                            aria-labelledby="product-reviews-tab"
                        >
                            @include('plugins/ecommerce::themes.includes.reviews')
                        </div>
                    @endif
                    @if (is_plugin_active('marketplace') && $product->store_id)
                        <div
                            class="tab-pane fade"
                            id="product-vendor-info"
                            role="tabpanel"
                            aria-labelledby="product-vendor-info-tab"
                        >
                            @include(Theme::getThemeNamespace() . '::views.marketplace.includes.info-box', [
                                'store' => $product->store,
                            ])
                        </div>
                    @endif
                    @if (is_plugin_active('faq') && count($product->faq_items) > 0)
                        <div
                            class="tab-pane fade"
                            id="product-faqs"
                            role="tabpanel"
                            aria-labelledby="product-faqs-tab"
                        >
                            @include('plugins/ecommerce::themes.includes.product-faqs', ['faqs' => $product->faq_items])
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@if (($relatedProducts = get_related_products($product, 6)) && $relatedProducts->isNotEmpty())
    {!! Theme::partial('ecommerce.product-grid-section', [
        'title' => __('Related products'),
        'products' => $relatedProducts,
        'wishlistIds' => $wishlistIds ?? []
    ]) !!}
@endif

{!! do_shortcode('[more-to-love][/more-to-love]') !!}

<!-- Mobile Sticky Add to Cart Bar -->
<div class="mobile-sticky-add-to-cart d-md-none">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="sticky-add-to-cart-wrapper">
                    <div class="quantity">
                        <div class="qty-group">
                            <a href="#" class="qty-decrease">-</a>
                            <input type="text" class="qty-input" value="1" min="1" max="100">
                            <a href="#" class="qty-increase">+</a>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn-add-to-cart">{{ __('Add to cart') }}</button>
                        <button class="btn-buy-now">{{ __('Buy Now') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Mobile Sticky Add to Cart Bar */
    .mobile-sticky-add-to-cart {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        padding: 10px 0;
        z-index: 999;
        display: none;
    }

    @media (max-width: 991px) {
        .mobile-sticky-add-to-cart {
            display: block;
        }

        .product-detail-container {
            padding-bottom: 80px;
        }
    }

    .sticky-add-to-cart-wrapper {
        display: flex;
        align-items: center;
    }

    .quantity {
        margin-right: 10px;
    }

    .qty-group {
        display: flex;
        align-items: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }

    .qty-decrease, .qty-increase {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;
        color: #333;
        font-size: 18px;
        text-decoration: none;
        font-weight: bold;
    }

    .qty-input {
        width: 40px;
        height: 30px;
        border: none;
        text-align: center;
        font-size: 14px;
        -moz-appearance: textfield;
    }

    .qty-input::-webkit-outer-spin-button,
    .qty-input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    .action-buttons {
        display: flex;
        flex: 1;
        gap: 10px;
    }

    .btn-add-to-cart, .btn-buy-now {
        flex: 1;
        height: 36px;
        border: none;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 500;
        color: #fff;
    }

    .btn-add-to-cart {
        background-color: var(--primary-color, #fab528);
    }

    .btn-buy-now {
        background-color: #ff6b6b;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const qtyDecrease = document.querySelector('.qty-decrease');
        const qtyIncrease = document.querySelector('.qty-increase');
        const qtyInput = document.querySelector('.qty-input');
        const addToCartBtn = document.querySelector('.btn-add-to-cart');
        const buyNowBtn = document.querySelector('.btn-buy-now');

        // Set initial value
        qtyInput.value = 1;

        // Decrease quantity
        qtyDecrease.addEventListener('click', function(e) {
            e.preventDefault();
            let value = parseInt(qtyInput.value);
            if (value > 1) {
                qtyInput.value = value - 1;
            }
        });

        // Increase quantity
        qtyIncrease.addEventListener('click', function(e) {
            e.preventDefault();
            let value = parseInt(qtyInput.value);
            qtyInput.value = value + 1;
        });

        // Initialize gallery immediately if EcommerceApp is available
        if (typeof EcommerceApp !== 'undefined') {
            EcommerceApp.initProductGallery();
        }



        // Add to cart
        addToCartBtn.addEventListener('click', function() {
            const form = document.querySelector('.product-details form.cart-form');
            if (form) {
                // Set quantity
                const formQty = form.querySelector('input[name="qty"]');
                if (formQty) {
                    formQty.value = qtyInput.value;
                }

                // Add hidden input for add to cart
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'add_to_cart';
                input.value = '1';
                form.appendChild(input);

                // Submit form
                form.submit();
            }
        });

        // Buy now
        buyNowBtn.addEventListener('click', function() {
            const form = document.querySelector('.product-details form.cart-form');
            if (form) {
                // Set quantity
                const formQty = form.querySelector('input[name="qty"]');
                if (formQty) {
                    formQty.value = qtyInput.value;
                }

                // Add hidden input for checkout
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'checkout';
                input.value = '1';
                form.appendChild(input);

                // Submit form
                form.submit();
            }
        });

        // Position the sticky bar above the footer
        function positionStickyBar() {
            const footerMobile = document.querySelector('.footer-mobile');
            const stickyBar = document.querySelector('.mobile-sticky-add-to-cart');

            if (footerMobile && stickyBar && window.innerWidth <= 991) {
                const footerHeight = footerMobile.offsetHeight;
                stickyBar.style.bottom = footerHeight + 'px';

                // Add padding to the page
                const container = document.querySelector('.product-detail-container');
                if (container) {
                    const stickyBarHeight = stickyBar.offsetHeight;
                    container.style.paddingBottom = (footerHeight + stickyBarHeight + 10) + 'px';
                }
            }
        }

        // Run on load and resize
        positionStickyBar();
        window.addEventListener('resize', positionStickyBar);

        // Run after a delay to ensure all elements are loaded
        setTimeout(positionStickyBar, 500);
    });
</script>

<!-- Final script to ensure price tag is visible and view more button is not hidden -->
<script>
    // Function to enhance product options UI
    function enhanceProductOptionsUI() {
        if (window.innerWidth <= 991) {
            // Add ripple effect to option selections
            var optionLabels = document.querySelectorAll('.attribute-swatches-wrapper label');
            optionLabels.forEach(function(label) {
                // Remove existing event listeners
                label.removeEventListener('click', addRippleEffect);

                // Add new event listener
                label.addEventListener('click', addRippleEffect);
            });

            // Fix for any missing styles
            var textSwatchLabels = document.querySelectorAll('.text-swatch li label');
            textSwatchLabels.forEach(function(label) {
                label.style.display = 'block';
                label.style.border = '1px solid #ddd';
                label.style.borderRadius = '4px';
                label.style.padding = '8px 15px';
                label.style.cursor = 'pointer';
                label.style.minWidth = '45px';
                label.style.textAlign = 'center';
            });

            var visualSwatchLabels = document.querySelectorAll('.visual-swatch li label');
            visualSwatchLabels.forEach(function(label) {
                label.style.display = 'block';
                label.style.width = '30px';
                label.style.height = '30px';
                label.style.borderRadius = '50%';
                label.style.overflow = 'hidden';
                label.style.border = '1px solid #ddd';
            });

            // Check for selected inputs and style their labels
            var selectedInputs = document.querySelectorAll('.attribute-swatches-wrapper input:checked');
            selectedInputs.forEach(function(input) {
                var label = input.nextElementSibling;
                if (label && label.tagName === 'LABEL') {
                    if (input.closest('.text-swatch')) {
                        label.style.backgroundColor = '#ff6633';
                        label.style.borderColor = '#ff6633';
                        label.style.color = '#fff';
                    } else if (input.closest('.visual-swatch')) {
                        label.style.border = '2px solid #ff6633';
                        label.style.transform = 'scale(1.1)';
                        label.style.boxShadow = '0 2px 8px rgba(255, 102, 51, 0.3)';
                    }
                }
            });
        }
    }

    // Function to add ripple effect
    function addRippleEffect(event) {
        // Create ripple element
        var ripple = document.createElement('span');
        ripple.classList.add('ripple-effect');
        this.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(function() {
            ripple.remove();
        }, 600);
    }

    // Function to update price tag width to match image width
    function updatePriceTagWidth() {
        if (window.innerWidth <= 991) {
            var mainImage = document.querySelector('.bb-product-gallery-images img');
            var priceElement = document.querySelector('.product-details-content .product-details .product-price');
            var galleryWrapper = document.querySelector('.bb-product-gallery-wrapper');

            if (mainImage && priceElement) {
                // Get the computed width of the image
                var computedStyle = window.getComputedStyle(mainImage);
                var imageWidth = mainImage.offsetWidth;

                // Apply the exact width to the price element
                priceElement.style.width = imageWidth + 'px';
                priceElement.style.maxWidth = '100%';

                // Also adjust the product details container if needed
                var productDetails = document.querySelector('.product-details-content .product-details');
                if (productDetails) {
                    productDetails.style.width = 'auto';
                    productDetails.style.maxWidth = '100%';
                }

                // Log the widths for debugging
                console.log('Image width: ' + imageWidth + 'px');
                console.log('Price tag width: ' + priceElement.offsetWidth + 'px');
            }
        }
    }

    // Run immediately
    (function() {
        if (window.innerWidth <= 991) {
            // Ensure View More button is visible with sticky bar
            var viewMoreButton = document.querySelector('.load-more-products');
            var endMessage = document.querySelector('.end-of-products');
            var stickyBar = document.querySelector('.mobile-sticky-add-to-cart');

            if (stickyBar && (viewMoreButton || (endMessage && !endMessage.classList.contains('d-none')))) {
                var stickyBarHeight = stickyBar.offsetHeight;

                if (viewMoreButton) {
                    var buttonContainer = viewMoreButton.closest('div');
                    if (buttonContainer) {
                        buttonContainer.style.marginBottom = (stickyBarHeight + 20) + 'px';
                        buttonContainer.style.paddingBottom = '20px';
                    }
                }

                if (endMessage && !endMessage.classList.contains('d-none')) {
                    endMessage.style.marginBottom = (stickyBarHeight + 20) + 'px';
                    endMessage.style.paddingBottom = '20px';
                }
            }

            // Move price tag before product title in product page
            var productDetails = document.querySelector('.product-details-content .product-details');
            var productGallery = document.querySelector('.bb-product-gallery-wrapper');

            if (productDetails && productGallery) {
                // Make the container a flex container with column direction
                productDetails.style.display = 'flex';
                productDetails.style.flexDirection = 'column';

                // Get the price element and the header element
                var priceElement = productDetails.querySelector('.product-price');
                var headerElement = productDetails.querySelector('.entry-product-header');

                // Get the exact width of the product gallery
                var galleryWidth = productGallery.offsetWidth;

                if (priceElement && headerElement) {
                    // Apply styles to the price element
                    priceElement.style.order = '-1';
                    priceElement.style.backgroundColor = '#ff6633';
                    priceElement.style.margin = '-10px 0 15px 0';
                    priceElement.style.padding = '10px 15px';
                    priceElement.style.position = 'relative';
                    priceElement.style.color = '#fff';
                    priceElement.style.display = 'block';
                    priceElement.style.boxSizing = 'border-box';

                    // Get the main product image
                    var mainImage = document.querySelector('.bb-product-gallery-images img');
                    if (mainImage) {
                        // Force the price tag to match the exact width of the main image
                        setTimeout(function() {
                            var imageWidth = mainImage.offsetWidth;
                            priceElement.style.width = imageWidth + 'px';
                            priceElement.style.maxWidth = '100%';
                        }, 100);
                    }

                    // Style the price text
                    var priceAmounts = priceElement.querySelectorAll('.price-amount bdi .amount');
                    priceAmounts.forEach(function(amount) {
                        amount.style.color = '#fff';
                        // Use larger font size for desktop (993px+)
                        amount.style.fontSize = window.innerWidth >= 993 ? '28px' : '18px';
                        amount.style.fontWeight = '700';
                    });

                    // Style the original price if it exists
                    var originalPrice = priceElement.querySelector('del .price-amount bdi .amount');
                    if (originalPrice) {
                        // Use larger font size for desktop original price too
                        originalPrice.style.fontSize = window.innerWidth >= 993 ? '18px' : '14px';
                        originalPrice.style.textDecoration = 'line-through';
                        originalPrice.style.opacity = '0.8';
                    }
                }
            }

            // Force all price tags to be visible
            var priceTags = document.querySelectorAll('.product-inner .product-details [style*="background-color: #ff6633"]');
            priceTags.forEach(function(tag) {
                tag.style.display = 'block';
                tag.style.backgroundColor = '#ff6633';
                tag.style.color = '#fff';
                tag.style.margin = '-10px -15px 15px -15px';
                tag.style.padding = '10px 15px';
                tag.style.width = 'calc(100% + 30px)';
                tag.style.position = 'relative';

                // Force all child elements to be visible
                var children = tag.querySelectorAll("*");
                children.forEach(function(child) {
                    // For price containers
                    if (child.style.display && child.style.display.includes("flex")) {
                        child.style.display = "flex";

                        // Check if this is a sale price (needs column layout)
                        var spans = child.querySelectorAll("span");
                        if (spans.length > 1) {
                            child.style.flexDirection = "column";
                            child.style.justifyContent = "center";
                            child.style.alignItems = "flex-start";
                            child.style.minHeight = "30px";
                        } else {
                            child.style.alignItems = "center";
                            child.style.justifyContent = "flex-start";
                            child.style.height = "30px";
                        }
                    }

                    // For sale price or regular price only
                    if (child.style.fontSize && child.style.fontSize.includes("18px")) {
                        child.style.fontSize = "18px";
                        child.style.fontWeight = "700";
                        child.style.color = "#fff";
                        child.style.lineHeight = "1.2";
                        child.style.textAlign = "left";
                    }

                    // For crossed-out original price
                    if (child.style.textDecoration && child.style.textDecoration.includes("line-through")) {
                        child.style.fontSize = "14px";
                        child.style.textDecoration = "line-through";
                        child.style.opacity = "0.8";
                        child.style.color = "#fff";
                        child.style.lineHeight = "1.2";
                        child.style.textAlign = "left";
                    }
                });
            });

            // Hide desktop price display
            var desktopPrices = document.querySelectorAll('.product-inner .product-details .d-none.d-md-block');
            desktopPrices.forEach(function(price) {
                price.style.display = 'none';
            });
        }
    })();

    // Add window resize event listener to update price tag width
    window.addEventListener('resize', updatePriceTagWidth);
    window.addEventListener('resize', enhanceProductOptionsUI);

    // Run functions after a short delay to ensure images are loaded
    setTimeout(updatePriceTagWidth, 300);
    setTimeout(updatePriceTagWidth, 1000);
    setTimeout(updatePriceTagWidth, 2000);

    // Run enhanceProductOptionsUI after DOM is loaded and after a delay
    enhanceProductOptionsUI();
    setTimeout(enhanceProductOptionsUI, 500);
    setTimeout(enhanceProductOptionsUI, 1500);

    // Add event listener for attribute changes
    document.addEventListener('change', function(event) {
        if (event.target.closest('.attribute-swatches-wrapper')) {
            console.log('=== Attribute change detected ===');
            console.log('Changed element:', event.target);
            console.log('Closest wrapper:', event.target.closest('.attribute-swatches-wrapper'));

            // Re-apply styling after a short delay to ensure the DOM has updated
            setTimeout(enhanceProductOptionsUI, 100);
        }
    });

    // Also listen for clicks on color swatches
    document.addEventListener('click', function(event) {
        const swatch = event.target.closest('.visual-swatch label, .text-swatch label');
        if (swatch) {
            console.log('=== Color swatch clicked ===');
            console.log('Swatch element:', swatch);
            console.log('Input element:', swatch.querySelector('input'));
        }
    });

    // Store original function if it exists
    if (typeof window.onChangeSwatchesSuccess === 'function') {
        window.originalOnChangeSwatchesSuccess = window.onChangeSwatchesSuccess;
    }

    // Enhanced product option change handler for gallery updates
    window.onChangeSwatchesSuccess = function(res, $attrs) {
        console.log('=== onChangeSwatchesSuccess called ===');
        console.log('Full response:', res);
        console.log('Attributes element:', $attrs);

        // First call the original function to handle all the standard functionality
        if (typeof window.originalOnChangeSwatchesSuccess === 'function') {
            console.log('Calling original function...');
            window.originalOnChangeSwatchesSuccess(res, $attrs);
        }

        // Then handle our custom gallery updates
        const { error, data } = res || {};

        console.log('Error:', error);
        console.log('Data:', data);

        if (!error && data) {
            console.log('Processing variation data...');

            // Update our custom gallery with variation images
            if (window.customGallery && typeof window.customGallery.updateGalleryForVariation === 'function') {
                let variationImages = [];

                // Check multiple possible image sources
                if (data.image_with_sizes) {
                    console.log('image_with_sizes found:', data.image_with_sizes);

                    if (data.image_with_sizes.origin) {
                        if (Array.isArray(data.image_with_sizes.origin)) {
                            variationImages = data.image_with_sizes.origin;
                        } else {
                            variationImages = [data.image_with_sizes.origin];
                        }
                    }
                } else if (data.image) {
                    console.log('Direct image found:', data.image);
                    variationImages = [data.image];
                } else {
                    console.log('No images found in variation data');
                }

                console.log('Final variation images to update:', variationImages);

                if (variationImages.length > 0) {
                    window.customGallery.updateGalleryForVariation(variationImages);
                } else {
                    console.log('No variation images to update, keeping original gallery');
                }
            } else {
                console.log('Custom gallery not available');
            }
        } else {
            console.log('Error or no data in response');
        }

        console.log('=== onChangeSwatchesSuccess completed ===');
    };

    // Force all attribute lists to be visible
    function forceAttributeVisibility() {
        if (window.innerWidth <= 991) {
            // Make all attribute lists visible
            var attributeLists = document.querySelectorAll(
                '.attribute-swatches-wrapper ul, ' +
                '.visual-swatch, ' +
                '.text-swatch, ' +
                '.attribute-swatch'
            );

            attributeLists.forEach(function(list) {
                list.style.display = 'flex';
                list.style.flexWrap = 'wrap';
                list.style.gap = '10px';
                list.style.visibility = 'visible';
                list.style.opacity = '1';
                list.style.padding = '0';
                list.style.margin = '0';
                list.style.listStyle = 'none';
            });

            // Make all attribute items visible
            var attributeItems = document.querySelectorAll(
                '.attribute-swatches-wrapper li'
            );

            attributeItems.forEach(function(item) {
                item.style.display = 'inline-block';
                item.style.visibility = 'visible';
                item.style.opacity = '1';
            });

            // Remove any empty product options containers
            removeEmptyOptionsContainers();
        }
    }

    // Function to remove empty product options containers
    function removeEmptyOptionsContainers() {
        // Find all product options wrappers
        var optionsWrappers = document.querySelectorAll('.pr_switch_wrap.product-options-wrapper');

        optionsWrappers.forEach(function(wrapper) {
            // Check if the wrapper has any visible content
            var hasContent = false;

            // Check for attribute swatches
            var swatches = wrapper.querySelectorAll('.attribute-swatches-wrapper');
            swatches.forEach(function(swatch) {
                if (swatch.querySelector('ul') && swatch.querySelector('ul').children.length > 0) {
                    hasContent = true;
                }
            });

            // Check for product options
            var options = wrapper.querySelectorAll('.product-options');
            options.forEach(function(option) {
                if (option.querySelector('.form-group') && option.querySelector('.form-group').children.length > 0) {
                    hasContent = true;
                }
            });

            // If no content, hide the wrapper
            if (!hasContent) {
                wrapper.style.display = 'none';
                wrapper.style.visibility = 'hidden';
                wrapper.style.height = '0';
                wrapper.style.overflow = 'hidden';
                wrapper.style.margin = '0';
                wrapper.style.padding = '0';
                wrapper.style.border = 'none';
            }
        });
    }

    // Run forceAttributeVisibility immediately and after delays
    forceAttributeVisibility();
    setTimeout(forceAttributeVisibility, 300);
    setTimeout(forceAttributeVisibility, 1000);

    // Also run removeEmptyOptionsContainers directly
    removeEmptyOptionsContainers();
    setTimeout(removeEmptyOptionsContainers, 500);
    setTimeout(removeEmptyOptionsContainers, 1500);

    // Use MutationObserver to detect changes in the DOM that might affect the image width or product options
    var observer = new MutationObserver(function(mutations) {
        updatePriceTagWidth();
        enhanceProductOptionsUI();
        forceAttributeVisibility();
        removeEmptyOptionsContainers();
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class', 'width']
    });

    // Run after a delay to ensure all elements are loaded
    setTimeout(function() {
        if (window.innerWidth <= 991) {
            // Ensure View More button is visible with sticky bar
            var viewMoreButton = document.querySelector('.load-more-products');
            var endMessage = document.querySelector('.end-of-products');
            var stickyBar = document.querySelector('.mobile-sticky-add-to-cart');

            if (stickyBar && (viewMoreButton || (endMessage && !endMessage.classList.contains('d-none')))) {
                var stickyBarHeight = stickyBar.offsetHeight;

                if (viewMoreButton) {
                    var buttonContainer = viewMoreButton.closest('div');
                    if (buttonContainer) {
                        buttonContainer.style.marginBottom = (stickyBarHeight + 20) + 'px';
                        buttonContainer.style.paddingBottom = '20px';
                    }
                }

                if (endMessage && !endMessage.classList.contains('d-none')) {
                    endMessage.style.marginBottom = (stickyBarHeight + 20) + 'px';
                    endMessage.style.paddingBottom = '20px';
                }
            }

            // Move price tag before product title in product page
            var productDetails = document.querySelector('.product-details-content .product-details');
            var productGallery = document.querySelector('.bb-product-gallery-wrapper');

            if (productDetails && productGallery) {
                // Make the container a flex container with column direction
                productDetails.style.display = 'flex';
                productDetails.style.flexDirection = 'column';

                // Get the price element and the header element
                var priceElement = productDetails.querySelector('.product-price');
                var headerElement = productDetails.querySelector('.entry-product-header');

                // Get the exact width of the product gallery
                var galleryWidth = productGallery.offsetWidth;

                if (priceElement && headerElement) {
                    // Apply styles to the price element
                    priceElement.style.order = '-1';
                    priceElement.style.backgroundColor = '#ff6633';
                    priceElement.style.margin = '-10px 0 15px 0';
                    priceElement.style.padding = '10px 15px';
                    priceElement.style.position = 'relative';
                    priceElement.style.color = '#fff';
                    priceElement.style.display = 'block';
                    priceElement.style.boxSizing = 'border-box';

                    // Get the main product image
                    var mainImage = document.querySelector('.bb-product-gallery-images img');
                    if (mainImage) {
                        // Force the price tag to match the exact width of the main image
                        setTimeout(function() {
                            var imageWidth = mainImage.offsetWidth;
                            priceElement.style.width = imageWidth + 'px';
                            priceElement.style.maxWidth = '100%';
                        }, 100);
                    }

                    // Style the price text
                    var priceAmounts = priceElement.querySelectorAll('.price-amount bdi .amount');
                    priceAmounts.forEach(function(amount) {
                        amount.style.color = '#fff';
                        // Use larger font size for desktop (993px+)
                        amount.style.fontSize = window.innerWidth >= 993 ? '28px' : '18px';
                        amount.style.fontWeight = '700';
                    });

                    // Style the original price if it exists
                    var originalPrice = priceElement.querySelector('del .price-amount bdi .amount');
                    if (originalPrice) {
                        // Use larger font size for desktop original price too
                        originalPrice.style.fontSize = window.innerWidth >= 993 ? '18px' : '14px';
                        originalPrice.style.textDecoration = 'line-through';
                        originalPrice.style.opacity = '0.8';
                    }
                }
            }

            // Force all price tags to be visible
            var priceTags = document.querySelectorAll('.product-inner .product-details [style*="background-color: #ff6633"]');
            priceTags.forEach(function(tag) {
                tag.style.display = 'block';
                tag.style.backgroundColor = '#ff6633';
                tag.style.color = '#fff';
                tag.style.margin = '-10px -15px 15px -15px';
                tag.style.padding = '10px 15px';
                tag.style.width = 'calc(100% + 30px)';
                tag.style.position = 'relative';

                // Force all child elements to be visible
                var children = tag.querySelectorAll("*");
                children.forEach(function(child) {
                    // For price containers
                    if (child.style.display && child.style.display.includes("flex")) {
                        child.style.display = "flex";

                        // Check if this is a sale price (needs column layout)
                        var spans = child.querySelectorAll("span");
                        if (spans.length > 1) {
                            child.style.flexDirection = "column";
                            child.style.justifyContent = "center";
                            child.style.alignItems = "flex-start";
                            child.style.minHeight = "30px";
                        } else {
                            child.style.alignItems = "center";
                            child.style.justifyContent = "flex-start";
                            child.style.height = "30px";
                        }
                    }

                    // For sale price or regular price only
                    if (child.style.fontSize && child.style.fontSize.includes("18px")) {
                        child.style.fontSize = "18px";
                        child.style.fontWeight = "700";
                        child.style.color = "#fff";
                        child.style.lineHeight = "1.2";
                        child.style.textAlign = "left";
                    }

                    // For crossed-out original price
                    if (child.style.textDecoration && child.style.textDecoration.includes("line-through")) {
                        child.style.fontSize = "14px";
                        child.style.textDecoration = "line-through";
                        child.style.opacity = "0.8";
                        child.style.color = "#fff";
                        child.style.lineHeight = "1.2";
                        child.style.textAlign = "left";
                    }
                });
            });

            // Hide desktop price display
            var desktopPrices = document.querySelectorAll('.product-inner .product-details .d-none.d-md-block');
            desktopPrices.forEach(function(price) {
                price.style.display = 'none';
            });
        }
    }, 1000);

    // Target the specific duplicate image that appears below the gallery
    setTimeout(function() {
        // Find the specific img element that's causing the issue
        const duplicateImg = document.querySelector('img[src*="Jellyfish-Colorful-Lamp-With-Bluetooth-Speaker"]');
        if (duplicateImg && !duplicateImg.closest('.custom-product-gallery')) {
            duplicateImg.style.display = 'none !important';
            console.log('Hidden duplicate image by src:', duplicateImg);
        }

        // Also target by alt text but be more specific
        const imgByAlt = document.querySelector('img[alt="Jellyfish Colorful Lamp With Bluetooth Speaker"]');
        if (imgByAlt && !imgByAlt.closest('.custom-product-gallery')) {
            imgByAlt.style.display = 'none !important';
            console.log('Hidden duplicate image by alt:', imgByAlt);
        }

        // Nuclear option - hide any img that's not in the gallery and has the product name
        document.querySelectorAll('img').forEach(function(img) {
            if (img.closest('.custom-product-gallery') || img.closest('.product-gallery')) {
                return; // Skip gallery images
            }

            if ((img.alt && img.alt.includes('Jellyfish Colorful Lamp')) ||
                (img.src && img.src.includes('Jellyfish-Colorful-Lamp'))) {
                img.style.display = 'none !important';
                console.log('Hidden duplicate image (nuclear):', img);
            }
        });
    }, 100);
</script>
